import mongoose from "mongoose";
import { <PERSON>NFIG } from "../config/environment";
import ClientModel from "../models/Client.model";

/**
 * Migration script to populate statusHistory for existing clients
 * This script adds initial status history for clients that don't have it
 */
async function migrateClientStatusHistory() {
  try {
    // Connect to MongoDB
    await mongoose.connect(CONFIG.MONGO_URI);
    console.log("Connected to MongoDB");

    // Find all clients without statusHistory or with empty statusHistory
    const clientsToUpdate = await ClientModel.find({
      $or: [
        { statusHistory: { $exists: false } },
        { statusHistory: { $size: 0 } }
      ]
    });

    console.log(`Found ${clientsToUpdate.length} clients to update`);

    let updatedCount = 0;

    for (const client of clientsToUpdate) {
      try {
        // Create initial status history entry based on current status and creation date
        const initialStatusHistory = [{
          status: client.isActive,
          changedAt: client.createdAt || new Date(),
          changedBy: client.therapistId
        }];

        // Update the client with status history
        await ClientModel.updateOne(
          { _id: client._id },
          { $set: { statusHistory: initialStatusHistory } }
        );

        updatedCount++;
        
        if (updatedCount % 100 === 0) {
          console.log(`Updated ${updatedCount} clients...`);
        }
      } catch (error) {
        console.error(`Error updating client ${client._id}:`, error);
      }
    }

    console.log(`Migration completed. Updated ${updatedCount} clients.`);
  } catch (error) {
    console.error("Migration failed:", error);
  } finally {
    await mongoose.disconnect();
    console.log("Disconnected from MongoDB");
  }
}

// Run the migration if this file is executed directly
if (require.main === module) {
  migrateClientStatusHistory()
    .then(() => {
      console.log("Migration script completed");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Migration script failed:", error);
      process.exit(1);
    });
}

export { migrateClientStatusHistory };
