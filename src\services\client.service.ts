import moment from "moment";
import { ClientDao } from "../lib/dao/client.dao";
import { ScheduleDao } from "../lib/dao/schedule.dao";
import { ScheduleStatus } from "../models/Schedule.model";
import { TransactionDao } from "../lib/dao/transaction.dao";
import { paymentStatus } from "../lib/enum/cashfree.enum";
import { CalendarEventDao } from "../lib/dao/calendarEvent.dao";
import { PayTrackerDao } from "../lib/dao/payTracker.dao";
import { GoogleCalendarService } from "./googleCalendar.service";
import { throwError } from "../util/response";
import { google } from "googleapis";
import { CONFIG } from "../config/environment";
import LogsModel from "../models/Logs.model";
import { ScheduleService } from "./schedule.service";
import { TransactionService } from "./transaction.service";
import Razorpay from "razorpay";
import RazorpayService from "./razorpay.service";
import { PaymentTrackerStatusEnum } from "../models/PayTracker.model";
import { PayTrackerService } from "../services/payTracker.service";

export class ClientService {
  static async create(payload: any) {
    return await ClientDao.create(payload);
  }

  static async getAllClient(
    therapistId: any,
    skip?: any,
    pageSize?: any,
    searchText?: any,
    startDate?: Date,
    endDate?: Date,
    isActive?: boolean,
    fromPublicCalender?: boolean
  ) {
    return await ClientDao.getAllClient(
      therapistId,
      skip,
      pageSize,
      searchText,
      startDate,
      endDate,
      isActive,
      fromPublicCalender
    );
  }

  static async getClientById(clientId: any) {
    return await ClientDao.getClientById(clientId);
  }

  static async findClientByEmail(clientEmail: string, therapistId: any) {
    return await ClientDao.findClientByEmail(clientEmail, therapistId);
  }

  static async getClientByEmail(email: string, therapistId: any) {
    return await ClientDao.getClientByEmail(email, therapistId);
  }

  static async getAllClientsByTherapist(therapistId: any) {
    return await ClientDao.getAllClientsByTherapist(therapistId);
  }

  static async getAll(pageSize: any, skip: any, therapistId: any) {
    return await ClientDao.getAll(pageSize, skip, therapistId);
  }

  static async getRequiredClients(therapistId: any, paginationClients: any) {
    let requiredClients = [];
    for (let paginationClient of paginationClients) {
      const totalClientSchedules =
        await ScheduleDao.getScheduleByTherapistAndClientId(
          therapistId,
          paginationClient._id
        );
      if (!totalClientSchedules) {
        continue;
      }
      const completedSchedules =
        await totalClientSchedules?.recurrenceDates.filter(
          (recurr) => recurr.status == ScheduleStatus.COMPLETED
        );
      await requiredClients.push({
        ...paginationClient,
        completedSchedules: completedSchedules.length,
      });
    }
    return await requiredClients;
  }

  static async getNewClients(
    therapistId: any,
    firstDayOfMonth: any,
    lastDayOfMonth: any
  ) {
    return await ClientDao.getNewClients(
      therapistId,
      firstDayOfMonth,
      lastDayOfMonth
    );
  }

  static async getClientIdsByTherapist(therapistId: any) {
    return await ClientDao.getClientIdsByTherapist(therapistId);
  }

  /**
   * Get client counts based on date range and status history
   * @param therapistId - Therapist ID
   * @param startDate - Start date for filtering
   * @param endDate - End date for filtering
   * @returns Object with active, inactive, and new client counts
   */
  static async getClientCountsByDateRange(therapistId: any, startDate: Date, endDate: Date) {
    return await ClientDao.getClientCountsByDateRange(therapistId, startDate, endDate);
  }

  static async markClientInActive(clientId: any) {
    const client = await ClientDao.getClientById(clientId);
    if (!client) {
      return { success: false, message: "" };
    }
    const currentTime = moment();

    const schedules = await ScheduleDao.getAllSchedulesOfClient(client._id);

    // Step 1: Check for any future completed transactions
    for (let schedule of schedules) {
      if (schedule.recurrenceDates) {
        for (let recurrenceDate of schedule.recurrenceDates) {
          if (moment(recurrenceDate.fromDate).isAfter(currentTime)) {
            const transactions = await TransactionDao.getTransactionByRec_id(
              recurrenceDate._id
            );

            const hasCompletedTransaction = transactions.some(
              (tr) => tr.paymentStatus === paymentStatus.COMPLETE
            );
            const payTracker =
              await PayTrackerService.getPaytrackerByScheduleIdAndScheduleRecId(
                schedule._id,
                recurrenceDate._id
              );

            if (
              (payTracker &&
                payTracker.status === PaymentTrackerStatusEnum.Paid_On_Time) ||
              hasCompletedTransaction
            ) {
              return {
                success: false,
                message:
                  "Some client sessions in the future are already paid. Please refund them to deactivate the client.",
              };
            }
          }
        }
      }
    }
    client.isActive = false;
    let recc_ids_to_delete: any[] = [];

    if (schedules && schedules.length > 0) {
      for (let schedule of schedules) {
        if (schedule.recurrenceDates) {
          for (let recurrenceDate of schedule.recurrenceDates) {
            if (moment(recurrenceDate.fromDate).isAfter(currentTime)) {
              const transaction = await TransactionDao.getTransactionByRec_id(
                recurrenceDate._id
              );

              recc_ids_to_delete.push(recurrenceDate._id);

              for (let tr of transaction) {
                if (tr.paymentStatus == paymentStatus.PENDING) {
                  const status = await RazorpayService.cancelPaymentLink(
                    tr.paymentSessionId
                  );
                  if (!status) {
                    await TransactionService.cancelPayment(
                      tr._id,
                      client.therapistId
                    );
                  }
                }
              }
            }
          }
        }
      }
    }

    if (recc_ids_to_delete.length > 0) {
      const deletedPayments =
        await PayTrackerDao.deletePayTrackersByScheduleRecIds(
          recc_ids_to_delete
        );

      const calendarEvents = await CalendarEventDao.getEventsByRecIds(
        recc_ids_to_delete
      );
      const googleCalendarData = await GoogleCalendarService.findByTherapist(
        client.therapistId
      );

      if (!googleCalendarData) {
        // throwError("No Google Calender Data found.", 404);
        return { success: false, message: "" };
      }
      const oauth2Client = new google.auth.OAuth2(
        CONFIG.clientId,
        CONFIG.clientSecret,
        CONFIG.clientRedirectUrl
      );

      const tokens = {
        access_token: googleCalendarData.access_token,
        refresh_token: googleCalendarData.refresh_token, // you'll only get this once, so store it securely
      };

      if (!tokens) {
        // throwError("No Google Calender Data found.", 404);
        return { success: false, message: "" };
      }

      oauth2Client.setCredentials(tokens);
      const calendar = google.calendar({ version: "v3", auth: oauth2Client });

      for (let calendarEvent of calendarEvents) {
        try {
          await calendar.events.delete({
            calendarId: "primary",
            eventId: calendarEvent.id,
          });
        } catch (e) {
          console.log("Already Deleted", e);
        }

        const log = await LogsModel.create({
          therapist: client.therapistId,
          comments: "Delete Event - Client Inactive Deletion",
          changes: {
            calendarEvent: calendarEvent,
            clientId: client._id,
          },
        });

        await log.save();
        await ScheduleService.deleteRecurrance(calendarEvent.scheduleRecId);
        await calendarEvent.deleteOne();
      }
    }
    const updatedClient = await client.save();
    return { success: true, updatedClient };
  }

  static async findActiveClientByEmail(clientEmail: string, therapistId: any) {
    return await ClientDao.findActiveClientByEmail(clientEmail, therapistId);
  }
}
