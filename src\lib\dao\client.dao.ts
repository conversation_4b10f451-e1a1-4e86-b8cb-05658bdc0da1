import mongoose from "mongoose";
import { removeEmpty, removeEmpty<PERSON>eys } from "../../helper/custom.helper";
import ClientModel from "../../models/Client.model";
import ScheduleModel from "../../models/Schedule.model";
import { throwError } from "../../util/response";
import { TherapistDao } from "./therapist.dao";
import PayTrackerModel from "../../models/PayTracker.model";

export class ClientDao {
  static async create(payload: any) {
    const therapist = await TherapistDao.getTherapist(payload.therapistId);
    if (!therapist) return throwError("Therapist not found", 404);
    let randomPart = Math.floor(Math.random() * 10000)
      .toString()
      .padStart(4, "0");
    let clientId = therapist.identifier + "_" + randomPart;
    payload = { ...payload, clientId: clientId };
    return await ClientModel.create(payload);
  }

  static async getAllClientWithDateAndPagination(
    therapistId: any,
    skip?: any,
    pageSize?: any,
    startDate?: any,
    endDate?: any,
    searchText?: any
  ) {
    // const query = removeEmptyKeys({therapistId: therapistId, createdAt: {$gte: startDate, $lte: endDate}, $or: [
    //     {name: { $regex: searchText, $options: "i" }},
    //     {email: { $regex: searchText, $options: "i" }},
    // ]})
    let query = removeEmptyKeys({
      therapistId: String(therapistId),
      createdAt: { $gte: startDate, $lte: endDate },
    });
    if (searchText) {
      query["$or"] = [
        { name: { $regex: searchText, $options: "i" } },
        { email: { $regex: searchText, $options: "i" } },
      ];
    }

    return await ClientModel.find(query).skip(skip).limit(pageSize).lean();
  }

  //   static async getAllClient(
  //     therapistId: any,
  //     skip?: any,
  //     pageSize?: any,
  //     searchText?: any,
  //     startDate?: any,
  //     endDate?: any
  //   ) {
  //     let query = removeEmptyKeys({ therapistId: String(therapistId) });

  //     if (searchText) {
  //       query["$or"] = [
  //         { name: { $regex: searchText, $options: "i" } },
  //         { email: { $regex: searchText, $options: "i" } },
  //       ];
  //     }
  //     return await ClientModel.find(query).skip(skip).limit(pageSize).lean();
  //   }

  // static async getAllClient(
  //   therapistId: any,
  //   skip: number = 0,
  //   pageSize: number = 20,
  //   searchText: string = "",
  //   startDate?: Date,
  //   endDate?: Date,
  //   isActive?: boolean
  // ) {
  //   // Initialize the query with therapistId
  //   let query: any = { therapistId: String(therapistId) };

  //   if (typeof isActive === "boolean") {
  //     query.isActive = isActive;
  //   }

  //   // Add date range filtering if startDate or endDate is provided
  //   if (startDate || endDate) {
  //     query.createdAt = {};
  //     if (startDate) query.createdAt.$gte = startDate;
  //     if (endDate) query.createdAt.$lte = endDate;
  //   }

  //   // Add search filter if searchText is provided
  //   if (searchText) {
  //     query["$or"] = [
  //       { name: { $regex: searchText, $options: "i" } },
  //       { email: { $regex: searchText, $options: "i" } },
  //     ];
  //   }

  //   // Execute the query with pagination and return the results
  //   return await ClientModel.find(query).skip(skip).limit(pageSize).lean();
  // }

  static async getAllClient(
    therapistId: any,
    skip: number = 0,
    pageSize: number = 20,
    searchText: string = "",
    startDate?: Date,
    endDate?: Date,
    isActive?: boolean,
    fromPublicCalender?: boolean
  ) {
    // Initialize the query with therapistId
    let query: any = { therapistId: String(therapistId) };

    if (fromPublicCalender && fromPublicCalender === true) {
      query.fromPublicCalender = true;
    }

    if (typeof isActive === "boolean") {
      query.isActive = isActive;
    }

    // Add date range filtering if startDate or endDate is provided
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = startDate;
      if (endDate) query.createdAt.$lte = endDate;
    }

    // Add search filter if searchText is provided
    if (searchText) {
      query["$or"] = [
        { name: { $regex: searchText, $options: "i" } },
        { email: { $regex: searchText, $options: "i" } },
      ];
    }

    // Execute the query with sorting, then pagination, and return the results
    return await ClientModel.find(query)
      .collation({ locale: "en", strength: 2 })
      .sort({ name: 1 }) // Sort by name in ascending order
      .skip(skip)
      .limit(pageSize)
      .lean();
  }

  static async getAllRegularClient(therapistId: any) {
    let query: any = { therapistId: String(therapistId) };

    return await ClientModel.find(query).lean();
  }

  static async getAllClientsByTherapist(
    therapistId: any,
    searchText?: any,
    startDate?: Date,
    endDate?: Date,
    isActive?: boolean,
    fromPublicCalender?: boolean
  ) {
    let query = removeEmptyKeys({ therapistId: String(therapistId) });
    if (typeof isActive === "boolean") {
      query.isActive = isActive;
    }

    // Add date range filtering if startDate or endDate is provided
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = startDate;
      if (endDate) query.createdAt.$lte = endDate;
    }

    if (searchText) {
      query["$or"] = [
        { name: { $regex: searchText, $options: "i" } },
        { email: { $regex: searchText, $options: "i" } },
      ];
    }

    if (fromPublicCalender && fromPublicCalender === true) {
      query["fromPublicCalender"] = true;
    }
    return await ClientModel.find(query).lean();
  }

  static async getAllTherapistClientsWithDate(
    therapistId: any,
    startDate?: any,
    endDate?: any,
    searchText?: any
  ) {
    // const query = removeEmptyKeys({therapistId: therapistId, $or: [
    //     {name: { $regex: searchText, $options: "i" }},
    //     {email: { $regex: searchText, $options: "i" }},
    // ], createdAt: {$gte: startDate, $lte: endDate}})
    let query = removeEmptyKeys({
      therapistId: String(therapistId),
      createdAt: { $gte: startDate, $lte: endDate },
    });
    if (searchText) {
      query["$or"] = [
        { name: { $regex: searchText, $options: "i" } },
        { email: { $regex: searchText, $options: "i" } },
      ];
    }
    return await ClientModel.find(query);
  }

  static async getClientById(clientId: any) {
    return await ClientModel.findById({ _id: clientId });
  }

  static async findClientByEmail(clientEmail: string, therapistId: any) {
    return await ClientModel.find({
      therapistId: therapistId,
      $or: [{ email: { $regex: clientEmail, $options: "i" } }],
    });
  }

  static async getClientByEmail(email: string, therapistId: any) {
    return await ClientModel.findOne({ email, therapistId });
  }

  static async searchByEmailOrNameWithPagination(
    therapistId: any,
    searchText: any,
    skip?: any,
    pageSize?: any
  ) {
    return await ClientModel.find({
      therapistId: therapistId,
      $or: [
        { name: { $regex: searchText, $options: "i" } },
        { email: { $regex: searchText, $options: "i" } },
      ],
    })
      .skip(skip)
      .limit(pageSize);
  }

  static async searchByEmailOrName(therapistId: any, searchText: any) {
    return await ClientModel.find({
      therapistId: therapistId,
      $or: [
        { name: { $regex: searchText, $options: "i" } },
        { email: { $regex: searchText, $options: "i" } },
      ],
    });
  }

  static async searchByEmail(therapistId: any, searchText: any) {
    return await ClientModel.find({
      therapistId: therapistId,
      $or: [{ email: { $regex: searchText, $options: "i" } }],
    });
  }

  static async searchByName(therapistId: any, searchText: any) {
    return await ClientModel.find({
      therapistId: therapistId,
      $or: [{ name: { $regex: searchText, $options: "i" } }],
    });
  }

  static async findClientById(clientId: any) {
    return await ClientModel.findOne({ _id: clientId });
  }
  static async getClientByTherapistId(clientId: any, therapistId: any) {
    return await ClientModel.findOne({
      _id: clientId,
      therapistId: therapistId,
    });
  }

  static async getAll(pageSize: any, skip: any, therapistId: any) {
    const query = removeEmpty({ therapistId: therapistId });
    return await ClientModel.find(query, "_id clientId")
      .skip(skip)
      .limit(pageSize);
  }

  static async updateClient(therapistId: any, clientId: any, clientData: any) {
    return await ClientModel.findOneAndUpdate(
      { therapistId: therapistId, _id: clientId },
      {
        $set: clientData,
      },
      { new: true }
    );
  }

  static async getNewClients(
    therapistId: any,
    firstDayOfMonth: any,
    lastDayOfMonth: any
  ) {
    return await ClientModel.find({
      therapistId: therapistId,
      createdAt: { $gte: firstDayOfMonth, $lte: lastDayOfMonth },
    });
  }

  static async getClientIdsByTherapist(therapistId: any) {
    return await ClientModel.find({ therapistId: therapistId }, "clientId");
  }

  static async getPending(therapistId: any, startDate: any, endDate: any) {
    const [totalPending] = await ScheduleModel.aggregate([
      {
        $unwind: "$recurrenceDates",
      },
      {
        $match: {
          therapistId: new mongoose.Types.ObjectId(therapistId),
          "recurrenceDates.fromDate": {
            $gte: new Date(startDate),
            $lte: new Date(endDate),
          },
        },
      },
      {
        $project: {
          _id: 1,
          "recurrenceDates.fromDate": 1,
          "recurrenceDates.toDate": 1,
          "recurrenceDates.payTrackerId": 1,
        },
      },
      {
        $lookup: {
          from: "paytracker",
          localField: "recurrenceDates.payTrackerId",
          foreignField: "_id",
          as: "payTrackerId",
          pipeline: [
            {
              $project: {
                _id: 1,
                amount: 1,
                status: 1,
              },
            },
          ],
        },
      },
      {
        $unwind: "$payTrackerId",
      },
      {
        $match: {
          "payTrackerId.status": "Still pending",
        },
      },
      {
        $group: {
          _id: null,
          totalAmount: {
            $sum: "$payTrackerId.amount.value",
          },
        },
      },
      {
        $project: {
          _id: 0,
          totalAmount: 1,
        },
      },
    ]);

    return totalPending?.totalAmount || 0;
  }

  static async defaultSession(clientId: any, newAmount: any) {
    const data = await ScheduleModel.aggregate([
      {
        $unwind: "$recurrenceDates",
      },
      {
        $match: {
          clientId: new mongoose.Types.ObjectId(clientId),
        },
      },
      {
        $project: {
          _id: 1,
          "recurrenceDates._id": 1,
          "recurrenceDates.fromDate": 1,
          "recurrenceDates.toDate": 1,
          "recurrenceDates.status": 1,
          "recurrenceDates.payTrackerId": 1,
        },
      },
      {
        $lookup: {
          from: "paytracker",
          localField: "recurrenceDates.payTrackerId",
          foreignField: "_id",
          as: "payTrackerId",
          pipeline: [
            {
              $project: {
                _id: 1,
                amount: 1,
                status: 1,
              },
            },
          ],
        },
      },
      {
        $unwind: "$payTrackerId",
      },
      {
        $match: {
          "payTrackerId.status": "Still pending",
          "recurrenceDates.status": {
            $in: ["confirmed", "rescheduled"],
          },
        },
      },
    ]);

    // Now that you have the relevant data, you can perform the updates
    for (const item of data) {
      // Update recurrenceDates.amount
      // await ScheduleModel.updateOne(
      //   {
      //     _id: item._id,
      //     "recurrenceDates._id": item.recurrenceDates._id,
      //   },
      //   {
      //     $set: {
      //       "recurrenceDates.$.amount": newAmount,
      //     },
      //   },
      //   {
      //     new: true,
      //   }
      // );

      await ScheduleModel.updateOne(
        {
          _id: item._id,
        },
        {
          $set: {
            "recurrenceDates.$[elem].amount": newAmount,
            publicCalendarAmountUpdated: true,
          },
        },
        {
          arrayFilters: [
            {
              "elem._id": item.recurrenceDates._id,
            },
          ],
          new: true,
        }
      );

      // Update payTrackerId.amount.value
      await PayTrackerModel.updateOne(
        {
          _id: item.payTrackerId._id,
        },
        {
          $set: {
            "amount.value": newAmount,
            publicCalendarAmountUpdated: true,
          },
        },
        {
          new: true,
        }
      );
    }
  }

  static async findActiveClientByEmail(clientEmail: string, therapistId: any) {
    return await ClientModel.find({
      therapistId: therapistId,
      $or: [{ email: { $regex: clientEmail, $options: "i" } }],
      isActive: true,
    });
  }

  /**
   * Get client counts based on date range and status history
   * @param therapistId - Therapist ID
   * @param startDate - Start date for filtering
   * @param endDate - End date for filtering
   * @returns Object with active, inactive, and new client counts
   */
  static async getClientCountsByDateRange(therapistId: any, startDate: Date, endDate: Date) {
    const pipeline = [
      {
        $match: {
          therapistId: new mongoose.Types.ObjectId(therapistId)
        }
      },
      {
        $addFields: {
          // Get the latest status at the end date
          statusAtEndDate: {
            $let: {
              vars: {
                relevantHistory: {
                  $filter: {
                    input: "$statusHistory",
                    cond: { $lte: ["$$this.changedAt", endDate] }
                  }
                }
              },
              in: {
                $cond: {
                  if: { $gt: [{ $size: "$$relevantHistory" }, 0] },
                  then: {
                    $arrayElemAt: [
                      {
                        $map: {
                          input: { $slice: [{ $sortArray: { input: "$$relevantHistory", sortBy: { changedAt: -1 } } }, 1] },
                          as: "item",
                          in: "$$item.status"
                        }
                      },
                      0
                    ]
                  },
                  else: true // Default to active if no history
                }
              }
            }
          },
          // Check if client was created in the date range
          isNewInRange: {
            $and: [
              { $gte: ["$createdAt", startDate] },
              { $lte: ["$createdAt", endDate] }
            ]
          }
        }
      },
      {
        $group: {
          _id: null,
          totalActive: {
            $sum: {
              $cond: [{ $eq: ["$statusAtEndDate", true] }, 1, 0]
            }
          },
          totalInactive: {
            $sum: {
              $cond: [{ $eq: ["$statusAtEndDate", false] }, 1, 0]
            }
          },
          totalNew: {
            $sum: {
              $cond: ["$isNewInRange", 1, 0]
            }
          }
        }
      }
    ];

    const result = await ClientModel.aggregate(pipeline);

    if (result.length === 0) {
      return {
        active: 0,
        inactive: 0,
        new: 0
      };
    }

    return {
      active: result[0].totalActive || 0,
      inactive: result[0].totalInactive || 0,
      new: result[0].totalNew || 0
    };
  }
}
