import { Schema, model, Document } from "mongoose";
import TherapistModel from "./Therapist.model";

export enum GenderEnum {
  MALE = "Cisgender Male",
  FEMALE = "Cisgender Female",
  TRANSGENDER = "Transgender",
  NON_BINARY = "Non-Binary",
  PREFER_NOT_TO_SAY = "Prefer not to say",
}
export interface IClient extends Document {
  therapistId: Schema.Types.ObjectId;
  email: string;
  name: string;
  phone: string;
  gender: GenderEnum | "";
  age: string;
  password: string;
  image: string;
  clientTimeZone: string;
  address: string;
  clientId: string;
  isActive: boolean;
  defaultSessionAmount: string;
  defaultTimezone: string;
  fromPublicCalender: boolean;
  statusHistory: Array<{
    status: boolean;
    changedAt: Date;
    changedBy?: Schema.Types.ObjectId;
  }>;
}

const clientSchema = new Schema<IClient>(
  {
    therapistId: {
      type: Schema.Types.ObjectId,
      ref: TherapistModel,
    },
    email: { type: String },
    password: { type: String },
    name: { type: String },
    image: { type: String },
    phone: String,
    address: String,
    gender: {
      type: String,
      enum: [...Object.values(GenderEnum), ""], // Allow empty string
      default: "", // Optional: set empty string as default if needed
    },
    age: String,
    clientTimeZone: String,
    clientId: String,
    isActive: { type: Boolean, default: true },
    defaultSessionAmount: String,
    defaultTimezone: { type: String, default: "Asia/Kolkata" },
    fromPublicCalender: { type: Boolean, default: false },
    statusHistory: [{
      status: { type: Boolean, required: true },
      changedAt: { type: Date, required: true },
      changedBy: { type: Schema.Types.ObjectId, ref: TherapistModel }
    }],
  },
  {
    versionKey: false,
    timestamps: true,
    collection: "client",
  }
);

// Pre-save middleware to track status changes
clientSchema.pre('save', function(next) {
  if (this.isNew) {
    // For new clients, add initial status to history
    this.statusHistory = [{
      status: this.isActive,
      changedAt: new Date(),
      changedBy: this.therapistId
    }];
  } else if (this.isModified('isActive')) {
    // For existing clients, add status change to history
    this.statusHistory.push({
      status: this.isActive,
      changedAt: new Date(),
      changedBy: this.therapistId
    });
  }
  next();
});

export default model<IClient>("client", clientSchema);
